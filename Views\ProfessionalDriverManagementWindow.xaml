<Window x:Class="DriverManagementSystem.Views.ProfessionalDriverManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:system="clr-namespace:System;assembly=mscorlib"
        xmlns:local="clr-namespace:DriverManagementSystem"
        Title="إدارة السائقين الاحترافية"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F7FA"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Null to Boolean Converter -->
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="15" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButton" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="10"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A6268"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#495057"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Primary Button -->
        <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#007BFF"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#0056B3"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#FFC107"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0A800"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="White" Height="80" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid Margin="30,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#667eea" CornerRadius="50" Width="50" Height="50" Margin="0,0,15,0">
                        <TextBlock Text="🚗" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة السائقين الاحترافية" FontSize="22" FontWeight="Bold" Foreground="#2C3E50"/>
                        <TextBlock Text="نظام شامل لإدارة السائقين والعقود والرحلات" FontSize="12" Foreground="#7F8C8D"/>
                    </StackPanel>
                </StackPanel>

                <!-- Search Box -->
                <Border Grid.Column="1" Background="#F8F9FA" CornerRadius="25" Height="40" Margin="50,0"
                       BorderBrush="#DEE2E6" BorderThickness="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="🔍" FontSize="16" VerticalAlignment="Center"
                                  Margin="15,0,10,0" Foreground="#6C757D"/>
                        <TextBox Grid.Column="1" x:Name="SearchTextBox"
                                Background="Transparent" BorderThickness="0"
                                FontSize="14" VerticalAlignment="Center"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Tag="البحث في السائقين (الاسم، الكود، الهاتف)"/>
                        <Button Grid.Column="2" Background="Transparent" BorderThickness="0"
                               Width="30" Height="30" Margin="5,0" Cursor="Hand"
                               ToolTip="مسح البحث" Click="ClearSearch_Click">
                            <TextBlock Text="✖" FontSize="12" Foreground="#6C757D"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Close Button -->
                <Button Grid.Column="2" x:Name="CloseButton" Click="CloseButton_Click"
                       Background="#DC3545" Foreground="White"
                       Width="40" Height="40"
                       BorderThickness="0" Cursor="Hand"
                       ToolTip="إغلاق">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                   CornerRadius="20"
                                   BorderThickness="{TemplateBinding BorderThickness}"
                                   BorderBrush="{TemplateBinding BorderBrush}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                    <TextBlock Text="✖" FontSize="16" FontWeight="Bold"/>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Statistics and Filters -->
            <Border Grid.Column="0" Style="{StaticResource ModernCard}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Statistics Section -->
                        <TextBlock Text="📊 الإحصائيات العامة" FontSize="16" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,0,0,15"/>

                        <!-- Statistics Cards -->
                        <Border Background="#E3F2FD" CornerRadius="10" Padding="15" Margin="0,0,0,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="👥" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding TotalDriversCount, FallbackValue='25'}" FontSize="18" FontWeight="Bold" Foreground="#1976D2"/>
                                    <TextBlock Text="إجمالي السائقين" FontSize="11" Foreground="#666"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <Border Background="#E8F5E8" CornerRadius="10" Padding="15" Margin="0,0,0,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="✅" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding AvailableDriversCount, FallbackValue='18'}" FontSize="18" FontWeight="Bold" Foreground="#388E3C"/>
                                    <TextBlock Text="السائقين المتاحين" FontSize="11" Foreground="#666"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <Border Background="#FFF3E0" CornerRadius="10" Padding="15" Margin="0,0,0,10"
                                ToolTip="{Binding SelectedDriversDisplayText}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🎯" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding SelectedDriversCount, FallbackValue='3'}" FontSize="18" FontWeight="Bold" Foreground="#F57C00"/>
                                    <TextBlock Text="السائقين المحددين" FontSize="11" Foreground="#666"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <Border Background="#FCE4EC" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="📋" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding ActiveContractsCount, FallbackValue='12'}" FontSize="18" FontWeight="Bold" Foreground="#C2185B"/>
                                    <TextBlock Text="العقود النشطة" FontSize="11" Foreground="#666"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- Vehicle Type Filters -->
                        <TextBlock Text="🚗 فلترة حسب نوع المركبة" FontSize="14" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,0,0,10"/>

                        <StackPanel>
                            <CheckBox x:Name="ForshanalFilter" Tag="فورشنال" IsChecked="{Binding IsForshanalSelected, Mode=TwoWay}"
                                     Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                     Margin="0,5" FontSize="12">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚐" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="فورشنال" FontWeight="SemiBold"/>
                                </StackPanel>
                            </CheckBox>

                            <CheckBox x:Name="KanterFilter" Tag="كنتر" IsChecked="{Binding IsKanterSelected, Mode=TwoWay}"
                                     Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                     Margin="0,5" FontSize="12">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚚" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="كنتر" FontWeight="SemiBold"/>
                                </StackPanel>
                            </CheckBox>

                            <CheckBox x:Name="HiluxFilter" Tag="هيلوكس" IsChecked="{Binding IsHiluxSelected, Mode=TwoWay}"
                                     Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                     Margin="0,5" FontSize="12">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚙" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="هيلوكس" FontWeight="SemiBold"/>
                                </StackPanel>
                            </CheckBox>

                            <CheckBox x:Name="BusFilter" Tag="حافلة" IsChecked="{Binding IsBusSelected, Mode=TwoWay}"
                                     Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                     Margin="0,5" FontSize="12">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚌" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="حافلة" FontWeight="SemiBold"/>
                                </StackPanel>
                            </CheckBox>

                            <CheckBox x:Name="PradoFilter" Tag="برادو" IsChecked="{Binding IsPradoSelected, Mode=TwoWay}"
                                     Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                     Margin="0,5" FontSize="12">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚗" FontSize="14" Margin="0,0,8,0"/>
                                    <TextBlock Text="برادو" FontWeight="SemiBold"/>
                                </StackPanel>
                            </CheckBox>
                        </StackPanel>

                        <!-- Quick Actions -->
                        <TextBlock Text="⚡ إجراءات سريعة" FontSize="14" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,20,0,10"/>

                        <Button Style="{StaticResource SuccessButton}" Height="35" Margin="0,5"
                               Click="SelectAllDrivers_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="✅" FontSize="12" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديد الكل" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ProfessionalButton}" Height="35" Margin="0,5"
                               Click="ClearSelection_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="❌" FontSize="12" Margin="0,0,5,0"/>
                                <TextBlock Text="إلغاء التحديد" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource PrimaryButton}" Height="35" Margin="0,5"
                               Click="RefreshData_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄" FontSize="12" Margin="0,0,5,0"/>
                                <TextBlock Text="تحديث البيانات" FontSize="12"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Center Panel - Drivers DataGrid -->
            <Border Grid.Column="2" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- DataGrid Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="10,10,0,0" Padding="15,10" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="📋 قائمة السائقين" FontSize="16" FontWeight="Bold" Foreground="#2C3E50"/>
                            <TextBlock Grid.Column="1" Text="{Binding FilteredDrivers.Count, FallbackValue='0'}"
                                      FontSize="14" FontWeight="Bold" Foreground="#007BFF"/>
                        </Grid>
                    </Border>

                    <!-- Drivers DataGrid -->
                    <DataGrid Grid.Row="1" x:Name="DriversDataGrid"
                             ItemsSource="{Binding FilteredDrivers}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="True"
                             SelectionChanged="DriversDataGrid_SelectionChanged"
                             CanUserResizeColumns="True"
                             CanUserSortColumns="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Extended"
                             AlternatingRowBackground="#F8F9FA"
                             RowBackground="White"
                             FontSize="12"
                             RowHeight="45">

                        <DataGrid.Columns>
                            <!-- Selection Column -->
                            <DataGridCheckBoxColumn Header="اختيار" Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}" Width="60"/>

                            <!-- Driver Code -->
                            <DataGridTextColumn Header="كود السائق" Binding="{Binding DriverCode}" Width="100" IsReadOnly="True"/>

                            <!-- Driver Name -->
                            <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="150" IsReadOnly="True"/>

                            <!-- Phone Number -->
                            <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="120" IsReadOnly="True"/>

                            <!-- Vehicle Type -->
                            <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="100" IsReadOnly="True"/>

                            <!-- Vehicle Number -->
                            <DataGridTextColumn Header="رقم المركبة" Binding="{Binding VehicleNumber}" Width="100" IsReadOnly="True"/>

                            <!-- Status -->
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80" IsReadOnly="True"/>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>

                    <!-- DataGrid Footer -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,10,10" Padding="15,10" Margin="0,10,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{Binding SelectedDrivers.Count, StringFormat='تم اختيار {0} سائق'}"
                                      FontSize="12" FontWeight="SemiBold" Foreground="#28A745"/>
                            <TextBlock Grid.Column="1" Text="{Binding FilteredDrivers.Count, StringFormat='من أصل {0} سائق'}"
                                      FontSize="12" Foreground="#6C757D"/>
                        </Grid>
                    </Border>
                </Grid>
            </Border>

            <!-- Right Panel - Driver Details -->
            <Border Grid.Column="4" Style="{StaticResource ModernCard}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="📄 تفاصيل السائق المحدد" FontSize="16" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,0,0,15"/>

                        <!-- Driver Details Content -->
                        <StackPanel x:Name="DriverDetailsPanel" DataContext="{Binding SelectedItem, ElementName=DriversDataGrid}">
                            <StackPanel.Style>
                                <Style TargetType="StackPanel">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding}" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Name}" Value="">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Name}" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </StackPanel.Style>

                            <!-- Driver Info Card -->
                            <Border Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="👤 معلومات السائق" FontSize="14" FontWeight="Bold"
                                              Foreground="#2C3E50" Margin="0,0,0,10"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="الكود:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding DriverCode}" Margin="0,5"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الاسم:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Name}" Margin="0,5"/>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الهاتف:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PhoneNumber}" Margin="0,5"/>

                                        <TextBlock Grid.Row="3" Grid.Column="0" Text="الحالة:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Status, FallbackValue='متاح'}" Margin="0,5"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- Vehicle Info Card -->
                            <Border Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="🚗 معلومات المركبة" FontSize="14" FontWeight="Bold"
                                              Foreground="#2C3E50" Margin="0,0,0,10"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="النوع:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding VehicleType, FallbackValue='غير محدد'}" Margin="0,5"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الرقم:" FontWeight="SemiBold" Margin="0,5,10,5"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding VehicleNumber, FallbackValue='غير محدد'}" Margin="0,5"/>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- Action Buttons -->
                            <StackPanel Orientation="Vertical" Margin="0,10">
                                <Button Content="📞 اتصال" Style="{StaticResource PrimaryButton}" Height="35" Margin="0,5"
                                       Click="CallDriver_Click"/>
                                <Button Content="💬 رسالة" Style="{StaticResource SuccessButton}" Height="35" Margin="0,5"
                                       Click="MessageDriver_Click"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- No Selection Message -->
                        <TextBlock x:Name="NoSelectionMessage" Text="اختر سائق من القائمة لعرض تفاصيله"
                                  FontSize="12" Foreground="#6C757D" TextAlignment="Center" Margin="0,50">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding SelectedItem, ElementName=DriversDataGrid}" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- Footer Section -->
        <Border Grid.Row="2" Background="White" Height="70" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid Margin="30,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Information -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📊 الحالة:" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding StatusMessage, FallbackValue='جاهز للاستخدام'}" FontSize="12" Foreground="#28A745" Margin="0,0,20,0"/>

                    <TextBlock Text="⏰ آخر تحديث:" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding LastUpdateTime, FallbackValue='الآن'}" FontSize="12" Foreground="#6C757D"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="ConfirmSelectionButton" Click="ConfirmSelectionButton_Click"
                           Style="{StaticResource SuccessButton}" Height="40" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="تأكيد الاختيار" FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CancelButton" Click="CancelButton_Click"
                           Style="{StaticResource DangerButton}" Height="40" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء" FontSize="14"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>